# Temario: Aprender a Programar con PHP
## Libro Completo de Programación PHP desde Cero

---

## **PARTE I: FUNDAMENTOS**

### Capítulo 1: Introducción a PHP
- 1.1 ¿Qué es PHP?
- 1.2 Historia y evolución de PHP
- 1.3 Ventajas y desventajas de PHP
- 1.4 PHP vs otros lenguajes de programación
- 1.5 Casos de uso y aplicaciones reales
- 1.6 Versiones de PHP y compatibilidad

### Capítulo 2: Configuración del Entorno de Desarrollo
- 2.1 Instalación de PHP en diferentes sistemas operativos
- 2.2 Configuración de un servidor web (Apache/Nginx)
- 2.3 Instalación y configuración de MySQL/MariaDB
- 2.4 Uso de XAMPP, WAMP, MAMP
- 2.5 Configuración de un entorno con Docker
- 2.6 Editores de código recomendados (VS Code, PhpStorm)
- 2.7 Extensiones útiles para desarrollo PHP

### Capítulo 3: Sintaxis Básica de PHP
- 3.1 Estructura básica de un archivo PHP
- 3.2 Etiquetas de apertura y cierre
- 3.3 Comentarios en PHP
- 3.4 Instrucciones y punto y coma
- 3.5 Sensibilidad a mayúsculas y minúsculas
- 3.6 Primer programa: "Hola Mundo"

### Capítulo 4: Variables y Tipos de Datos
- 4.1 Declaración de variables
- 4.2 Reglas para nombres de variables
- 4.3 Tipos de datos primitivos
  - 4.3.1 Enteros (int)
  - 4.3.2 Números decimales (float)
  - 4.3.3 Cadenas de texto (string)
  - 4.3.4 Booleanos (bool)
  - 4.3.5 NULL
- 4.4 Variables variables
- 4.5 Constantes
- 4.6 Variables superglobales
- 4.7 Ámbito de variables (scope)

### Capítulo 5: Operadores
- 5.1 Operadores aritméticos
- 5.2 Operadores de asignación
- 5.3 Operadores de comparación
- 5.4 Operadores lógicos
- 5.5 Operadores de incremento y decremento
- 5.6 Operadores de concatenación
- 5.7 Operador ternario
- 5.8 Operador de fusión null (??)
- 5.9 Precedencia de operadores

---

## **PARTE II: ESTRUCTURAS DE CONTROL**

### Capítulo 6: Estructuras Condicionales
- 6.1 Estructura if
- 6.2 Estructura if-else
- 6.3 Estructura if-elseif-else
- 6.4 Operador ternario avanzado
- 6.5 Estructura switch
- 6.6 Match expression (PHP 8+)
- 6.7 Buenas prácticas en condicionales

### Capítulo 7: Estructuras de Repetición (Bucles)
- 7.1 Bucle while
- 7.2 Bucle do-while
- 7.3 Bucle for
- 7.4 Bucle foreach
- 7.5 Control de bucles: break y continue
- 7.6 Bucles anidados
- 7.7 Optimización de bucles

---

## **PARTE III: ESTRUCTURAS DE DATOS**

### Capítulo 8: Arrays (Arreglos)
- 8.1 Introducción a los arrays
- 8.2 Arrays indexados
- 8.3 Arrays asociativos
- 8.4 Arrays multidimensionales
- 8.5 Funciones básicas de arrays
- 8.6 Recorrido de arrays
- 8.7 Manipulación de arrays
- 8.8 Arrays como pilas y colas
- 8.9 Ordenamiento de arrays

### Capítulo 9: Cadenas de Texto (Strings)
- 9.1 Creación y manipulación de strings
- 9.2 Comillas simples vs comillas dobles
- 9.3 Concatenación de strings
- 9.4 Funciones básicas de strings
- 9.5 Búsqueda en strings
- 9.6 Reemplazo en strings
- 9.7 Formateo de strings
- 9.8 Expresiones regulares básicas

---

## **PARTE IV: FUNCIONES**

### Capítulo 10: Funciones Básicas
- 10.1 ¿Qué son las funciones?
- 10.2 Declaración de funciones
- 10.3 Parámetros y argumentos
- 10.4 Valores de retorno
- 10.5 Parámetros por defecto
- 10.6 Parámetros variables
- 10.7 Funciones anónimas (closures)
- 10.8 Funciones flecha (arrow functions)

### Capítulo 11: Funciones Avanzadas
- 11.1 Funciones recursivas
- 11.2 Funciones de orden superior
- 11.3 Callbacks
- 11.4 Funciones generadoras
- 11.5 Espacios de nombres (namespaces)
- 11.6 Autoloading de funciones

---

## **PARTE V: PROGRAMACIÓN ORIENTADA A OBJETOS**

### Capítulo 12: Introducción a POO
- 12.1 Conceptos básicos de POO
- 12.2 Clases y objetos
- 12.3 Propiedades y métodos
- 12.4 Constructor y destructor
- 12.5 Visibilidad (public, private, protected)
- 12.6 Palabra clave $this

### Capítulo 13: Conceptos Avanzados de POO
- 13.1 Herencia
- 13.2 Polimorfismo
- 13.3 Encapsulación
- 13.4 Abstracción
- 13.5 Clases abstractas
- 13.6 Interfaces
- 13.7 Traits
- 13.8 Métodos y propiedades estáticas

### Capítulo 14: Características Modernas de POO
- 14.1 Type declarations
- 14.2 Return type declarations
- 14.3 Nullable types
- 14.4 Union types (PHP 8+)
- 14.5 Constructor property promotion (PHP 8+)
- 14.6 Named arguments (PHP 8+)
- 14.7 Attributes (PHP 8+)

---

## **PARTE VI: MANEJO DE ARCHIVOS Y DATOS**

### Capítulo 15: Manejo de Archivos
- 15.1 Lectura de archivos
- 15.2 Escritura de archivos
- 15.3 Manipulación de archivos
- 15.4 Trabajo con directorios
- 15.5 Permisos de archivos
- 15.6 Subida de archivos
- 15.7 Manejo de archivos CSV
- 15.8 Trabajo con archivos JSON

### Capítulo 16: Bases de Datos con MySQL
- 16.1 Introducción a bases de datos
- 16.2 Conexión a MySQL con MySQLi
- 16.3 Conexión con PDO
- 16.4 Operaciones CRUD básicas
- 16.5 Consultas preparadas
- 16.6 Transacciones
- 16.7 Manejo de errores en BD
- 16.8 Buenas prácticas de seguridad

---

## **PARTE VII: DESARROLLO WEB**

### Capítulo 17: Formularios Web
- 17.1 Creación de formularios HTML
- 17.2 Métodos GET y POST
- 17.3 Validación de datos del lado del servidor
- 17.4 Sanitización de datos
- 17.5 Manejo de errores en formularios
- 17.6 Subida de archivos mediante formularios
- 17.7 Formularios dinámicos

### Capítulo 18: Sesiones y Cookies
- 18.1 ¿Qué son las sesiones?
- 18.2 Inicio y manejo de sesiones
- 18.3 Variables de sesión
- 18.4 Cookies: creación y lectura
- 18.5 Seguridad en sesiones y cookies
- 18.6 Autenticación de usuarios
- 18.7 Sistema de login/logout

### Capítulo 19: Manejo de Errores y Excepciones
- 19.1 Tipos de errores en PHP
- 19.2 Configuración de reporte de errores
- 19.3 Manejo de errores personalizado
- 19.4 Excepciones en PHP
- 19.5 Try-catch-finally
- 19.6 Creación de excepciones personalizadas
- 19.7 Logging de errores

---

## **PARTE VIII: TEMAS AVANZADOS**

### Capítulo 20: APIs y Servicios Web
- 20.1 ¿Qué es una API?
- 20.2 APIs REST
- 20.3 Consumo de APIs externas
- 20.4 Creación de APIs con PHP
- 20.5 Autenticación en APIs
- 20.6 Formato JSON
- 20.7 cURL en PHP

### Capítulo 21: Seguridad en PHP
- 21.1 Vulnerabilidades comunes
- 21.2 Inyección SQL
- 21.3 Cross-Site Scripting (XSS)
- 21.4 Cross-Site Request Forgery (CSRF)
- 21.5 Validación y sanitización
- 21.6 Encriptación y hashing
- 21.7 Buenas prácticas de seguridad

### Capítulo 22: Optimización y Rendimiento
- 22.1 Profiling de código PHP
- 22.2 Optimización de consultas a BD
- 22.3 Caché en PHP
- 22.4 Optimización de memoria
- 22.5 Herramientas de debugging
- 22.6 Mejores prácticas de rendimiento

---

## **PARTE IX: HERRAMIENTAS Y FRAMEWORKS**

### Capítulo 23: Composer y Gestión de Dependencias
- 23.1 ¿Qué es Composer?
- 23.2 Instalación y configuración
- 23.3 Archivo composer.json
- 23.4 Instalación de paquetes
- 23.5 Autoloading con Composer
- 23.6 Creación de paquetes propios

### Capítulo 24: Introducción a Frameworks
- 24.1 ¿Por qué usar frameworks?
- 24.2 Comparación de frameworks populares
- 24.3 Introducción a Laravel
- 24.4 Introducción a Symfony
- 24.5 Patrones de diseño en frameworks
- 24.6 MVC (Modelo-Vista-Controlador)

### Capítulo 25: Testing en PHP
- 25.1 Importancia del testing
- 25.2 Tipos de testing
- 25.3 PHPUnit básico
- 25.4 Escritura de tests unitarios
- 25.5 Mocking y stubs
- 25.6 Test-Driven Development (TDD)

---

## **PARTE X: PROYECTOS PRÁCTICOS**

### Capítulo 26: Proyecto 1 - Sistema de Gestión de Tareas
- 26.1 Análisis de requerimientos
- 26.2 Diseño de la base de datos
- 26.3 Estructura del proyecto
- 26.4 Implementación del CRUD
- 26.5 Interfaz de usuario
- 26.6 Autenticación de usuarios

### Capítulo 27: Proyecto 2 - Blog Personal
- 27.1 Planificación del proyecto
- 27.2 Sistema de posts
- 27.3 Sistema de comentarios
- 27.4 Panel de administración
- 27.5 SEO básico
- 27.6 Deployment

### Capítulo 28: Proyecto 3 - API REST para E-commerce
- 28.1 Diseño de la API
- 28.2 Gestión de productos
- 28.3 Sistema de usuarios
- 28.4 Carrito de compras
- 28.5 Procesamiento de pagos
- 28.6 Documentación de la API

---

## **APÉNDICES**

### Apéndice A: Referencia Rápida de Funciones PHP
### Apéndice B: Configuración de php.ini
### Apéndice C: Comandos útiles de línea de comandos
### Apéndice D: Recursos adicionales y comunidades
### Apéndice E: Glosario de términos
### Apéndice F: Soluciones a ejercicios

---

## **CARACTERÍSTICAS DEL LIBRO**

- **Nivel**: Principiante a Intermedio-Avanzado
- **Páginas estimadas**: 800-1000 páginas
- **Ejercicios**: Más de 200 ejercicios prácticos
- **Proyectos**: 3 proyectos completos paso a paso
- **Código**: Todo el código disponible en repositorio GitHub
- **Actualizaciones**: Compatible con PHP 8.x y versiones futuras
